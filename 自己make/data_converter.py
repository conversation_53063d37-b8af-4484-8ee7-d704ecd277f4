#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据集转换工具

提供将COCO/VOC格式转换为YOLO格式的功能
"""

import json
import shutil
import yaml
import sys
from pathlib import Path

# 添加项目根目录到系统路径
sys.path.insert(0, str(Path(__file__).resolve().parents[2]))

from BDT.yoloserver.utils.logging_utils import setup_logger
from BDT.yoloserver.utils.paths import RAW_TEMP_DIR, LOGS_DIR

from datetime import datetime

import random
import string  # 这个也需要导入，因为错误中使用了string.ascii_lowercase




# 初始化日志
logger = setup_logger(base_path=LOGS_DIR, log_type="data_conversion_lib", model_name=None)


# 初始化日志
logger = setup_logger(base_path=LOGS_DIR, log_type="data_conversion_lib", model_name=None)

def convert_annotations(input_dir, output_dir, annotation_format='coco', class_filter=None):
    """
    将原始标注文件转换为YOLO格式
    
    Args:
        input_dir: 输入目录路径，包含原始标注文件
        output_dir: 输出目录路径，用于保存转换后的YOLO格式标注文件
        annotation_format: 原始标注格式，可选'coco'或'voc'
        class_filter: 可选的类别过滤列表
        
    Returns:
        tuple: (success, classes) 转换是否成功和检测到的类别列表
    """
    input_dir = Path(input_dir)
    output_dir = Path(output_dir)
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        if annotation_format.lower() == 'coco':
            return convert_coco_to_yolo(input_dir, output_dir, class_filter)
        elif annotation_format.lower() == 'voc':
            return convert_voc_to_yolo(input_dir, output_dir, class_filter)
        else:
            logger.error(f"不支持的标注格式: {annotation_format}")
            return False, []
    except Exception as e:
        logger.error(f"转换过程中出现错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, []

def convert_coco_to_yolo(input_dir, output_dir, class_filter=None):
    """
    将COCO格式的标注转换为YOLO格式
    """
    # 创建一个临时目录用于处理
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    rand_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    temp_dir = RAW_TEMP_DIR / f"coco2yolo_{timestamp}_{rand_str}"
    temp_images_dir = temp_dir / "images"
    temp_labels_dir = temp_dir / "labels"
    
    # 创建临时目录结构
    temp_images_dir.mkdir(parents=True, exist_ok=True)
    temp_labels_dir.mkdir(parents=True, exist_ok=True)

    # 查找所有COCO格式的JSON文件
    coco_files = list(input_dir.glob("*.json"))
    if not coco_files:
        logger.error(f"未在 {input_dir} 找到COCO JSON文件")
        return False, []
    
    # 统一的类别列表
    all_classes = []
    processed_files = 0
    
    # 处理每个COCO文件
    for coco_file in coco_files:
        logger.info(f"处理COCO文件: {coco_file.name}")
        
        try:
            with open(coco_file, 'r', encoding='utf-8') as f:
                coco_data = json.load(f)
            
            # 提取类别和图像信息
            categories = {cat['id']: cat['name'] for cat in coco_data.get('categories', [])}
            images = {img['id']: img for img in coco_data.get('images', [])}
            
            # 更新全局类别列表
            for cat_id, cat_name in categories.items():
                if cat_name not in all_classes:
                    all_classes.append(cat_name)
            
            # 应用类别过滤
            if class_filter:
                filtered_classes = [c for c in all_classes if c in class_filter]
                if not filtered_classes:
                    logger.warning(f"过滤后无剩余类别! 跳过文件: {coco_file.name}")
                    continue
                all_classes = filtered_classes
            
            # 为每个图像生成YOLO格式的标注
            for annotation in coco_data.get('annotations', []):
                image_id = annotation['image_id']
                image_info = images.get(image_id)
                
                if not image_info:
                    continue
                
                cat_id = annotation['category_id']
                cat_name = categories.get(cat_id, '')
                
                # 如果设置了类别过滤，且当前类别不在过滤列表中，则跳过
                if class_filter and cat_name not in class_filter:
                    continue
                
                # 获取类别索引
                class_id = all_classes.index(cat_name)
                
                # 计算YOLO格式的坐标 (归一化到0-1)
                bbox = annotation['bbox']  # [x, y, width, height]
                img_width, img_height = image_info['width'], image_info['height']
                
                # COCO格式:  [左上角x, 左上角y, 宽度, 高度]
                # YOLO格式: [中心点x/图像宽度, 中心点y/图像高度, 宽度/图像宽度, 高度/图像高度]
                x_center = (bbox[0] + bbox[2] / 2) / img_width
                y_center = (bbox[1] + bbox[3] / 2) / img_height
                bbox_width = bbox[2] / img_width
                bbox_height = bbox[3] / img_height
                
                # 创建标签文件
                label_file = temp_labels_dir / f"{image_info['file_name'].split('.')[0]}.txt"
                
                # 写入或追加标注
                with open(label_file, 'a') as f:
                    f.write(f"{class_id} {x_center} {y_center} {bbox_width} {bbox_height}\n")
                
                processed_files += 1
        
        except Exception as e:
            logger.error(f"处理COCO文件 {coco_file.name} 时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    # 将临时目录中的标签文件复制到输出目录
    if processed_files > 0:
        try:
            # 复制所有生成的标签文件到输出目录
            label_files = list(temp_labels_dir.glob("*.txt"))
            for label_file in label_files:
                shutil.copy2(label_file, output_dir)
            
            logger.info(f"成功转换 {processed_files} 个标注到YOLO格式")
            return True, all_classes
        except Exception as e:
            logger.error(f"复制标签文件时出错: {e}")
            return False, []
    else:
        logger.warning("没有处理任何标注文件")
        return False, []

def convert_voc_to_yolo(input_dir, output_dir, class_filter=None):
    """
    将VOC格式的标注转换为YOLO格式
    """
    # VOC格式转换逻辑...
    logger.error("VOC格式转换暂未实现")
    return False, []

def generate_data_yaml(train_dir, val_dir, test_dir, classes, output_file):
    """
    生成YOLO训练所需的data.yaml配置文件
    
    Args:
        train_dir: 训练集图像目录
        val_dir: 验证集图像目录
        test_dir: 测试集图像目录
        classes: 类别列表
        output_file: 输出文件路径
        
    Returns:
        bool: 是否成功生成配置文件
    """
    try:
        data = {
            'path': str(Path(train_dir).parent.parent),  # 数据集根目录
            'train': str(train_dir),
            'val': str(val_dir),
            'test': str(test_dir),
            'nc': len(classes),  # 类别数量
            'names': classes     # 类别名称
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, default_flow_style=False, sort_keys=False)
        
        logger.info(f"成功生成data.yaml文件: {output_file}")
        return True
    except Exception as e:
        logger.error(f"生成data.yaml文件时出错: {e}")
        return False

if __name__ == "__main__":
    # 模块测试代码
    pass
