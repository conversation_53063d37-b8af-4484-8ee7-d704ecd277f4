#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据集转换脚本 - 统一入口

此脚本提供了数据标注格式转换的命令行接口
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到系统路径
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))

from utils.coco import convert_annotations, generate_data_yaml
from utils.paths import (
    ORIGINAL_ANNOTATIONS_DIR, YOLO_STAGED_LABELS_DIR, CONFIGS_DIR,
    TRAIN_DIR, VAL_DIR, TEST_DIR, LOGS_DIR
)
from utils.logging_utils import setup_logger

# 初始化日志
logger = setup_logger(base_path=LOGS_DIR, log_type="data_conversion", model_name=None)

def main():
    parser = argparse.ArgumentParser(description='数据集格式转换工具')
    parser.add_argument('--input-dir', type=str, default=str(ORIGINAL_ANNOTATIONS_DIR),
                        help='输入目录，包含原始标注文件')
    parser.add_argument('--output-dir', type=str, default=str(YOLO_STAGED_LABELS_DIR),
                        help='输出目录，用于存放转换后的YOLO格式标注文件')
    parser.add_argument('--format', type=str, choices=['coco', 'voc'], default='coco',
                        help='原始标注格式类型: coco 或 voc')
    parser.add_argument('--classes', type=str, nargs='+', default=None,
                        help='可选的类别过滤列表，不指定则使用所有检测到的类别')
    parser.add_argument('--gen-yaml', action='store_true',
                        help='是否生成data.yaml配置文件')
    parser.add_argument('--yaml-path', type=str, default=str(CONFIGS_DIR / 'data.yaml'),
                        help='data.yaml配置文件的保存路径')

    args = parser.parse_args()

    # 执行转换
    logger.info(f"开始将{args.format}格式标注文件从{args.input_dir}转换为YOLO格式，并保存到{args.output_dir}")
    success, classes = convert_annotations(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        annotation_format=args.format,
        class_filter=args.classes
    )

    if success:
        logger.info(f"转换成功！检测到的类别: {classes}")
        
        # 生成data.yaml
        if args.gen_yaml:
            logger.info(f"开始生成data.yaml配置文件: {args.yaml_path}")
            yaml_success = generate_data_yaml(
                train_dir=TRAIN_DIR / "images",
                val_dir=VAL_DIR / "images",
                test_dir=TEST_DIR / "images",
                classes=classes,
                output_file=args.yaml_path
            )
            if yaml_success:
                logger.info(f"data.yaml配置文件已成功生成")
            else:
                logger.error(f"生成data.yaml配置文件失败")
    else:
        logger.error(f"转换失败！")

if __name__ == "__main__":
    main()
