import torch
import torch.nn as nn
import numpy as np
import cv2
import sys
import platform
from importlib.metadata import version, PackageNotFoundError

print("=" * 40)
print("【系统基础信息】")
print("=" * 40)
print(f"操作系统: {platform.system()} {platform.release()}")
print(f"Python 版本: {sys.version.split()[0]}")
print(f"PyTorch 版本: {torch.__version__}")
print(f"NumPy 版本: {np.__version__}")
print(f"OpenCV 版本: {cv2.__version__}")
print(f"cuDNN 是否可用: {torch.backends.cudnn.is_available()}")
print(f"cuDNN 是否启用: {torch.backends.cudnn.enabled}")
print(f"cuDNN 版本: {torch.backends.cudnn.version() if torch.backends.cudnn.is_available() else 'N/A'}")

print("\n" + "=" * 40)
print("【CUDA 检测】")
print("=" * 40)
cuda_available = torch.cuda.is_available()
print("CUDA 是否可用:", cuda_available)

if cuda_available:
    print("CUDA 版本:", torch.version.cuda)
    gpu_count = torch.cuda.device_count()
    print("可用 GPU 数量:", gpu_count)

    for i in range(gpu_count):
        props = torch.cuda.get_device_properties(i)
        print(f"\n--- GPU {i} 信息 ---")
        print(f"GPU 名称: {torch.cuda.get_device_name(i)}")
        print(f"  显存: {props.total_memory / 1024 ** 3:.2f} GB")
        print(f"  计算能力: {props.major}.{props.minor}")
        print(f"  多处理器数量: {props.multi_processor_count}")
        print(f"  当前已分配显存: {torch.cuda.memory_allocated(i) / 1024 ** 2:.2f} MB")
        print(f"  当前已保留显存: {torch.cuda.memory_reserved(i) / 1024 ** 2:.2f} MB")

    # 简单张量测试
    print("\n" + "=" * 40)
    print("【张量 & 模型测试】")
    print("=" * 40)
    try:
        a = torch.randn(1000, 1000).cuda()
        b = torch.randn(1000, 1000).cuda()
        c = a + b
        print("张量 a 所在设备:", a.device)
        print("加法结果 c 的设备:", c.device)

        # 模型测试
        model = nn.Linear(1000, 10).cuda()
        x = torch.randn(1, 1000).cuda()
        output = model(x)
        print("模型所在设备:", next(model.parameters()).device)
        print("模型前向输出 shape:", output.shape)
    except RuntimeError as e:
        print("运行时错误:", e)

else:
    print("当前环境未启用 CUDA，正在使用 CPU。")

print("\n" + "=" * 40)
print("【常用库版本检测】")
print("=" * 40)

def get_version(pkg_name):
    try:
        return version(pkg_name)
    except PackageNotFoundError:
        return "未安装"
    except Exception as err:  # ✅ 避免与外部 `e` 冲突
        return f"获取失败: {type(err).__name__}"

for pkg in ["onnx", "ultralytics", "torchvision", "Pillow"]:
    print(f"{pkg} 版本:", get_version(pkg))
