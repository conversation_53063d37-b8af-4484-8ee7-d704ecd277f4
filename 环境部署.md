网络： 教室1， 教室1-5G: 12345678

western-cloud-valley:  abc123456

```
- yoloserver
├── configs/                                # 配置文件
├── data/                                   # 数据集
│   ├── raw/                                # 原始数据
│   │   ├── images/                         # 原始图像
│   │   └── annotations/                    # 原始 COCO JSON 标注
│   ├── train/                              # 训练集
│   │   ├── images/
│   │   └── labels/
│   ├── val/                                # 验证集
│   │   ├── images/
│   │   └── labels/
│   └── test/                               # 测试集
│       ├── images/
│       └── labels/
├── logging/                                # 日志
│   ├── project_init/                       # 初始化日志
│   ├── data_conversion/                    # 转换日志
│   ├── train/                              # 训练日志
│   ├── val/                                # 验证日志
│   └── infer/                              # 推理日志
├── runs/                                   # 训练/验证/推理结果
│   ├── detect/                             # 训练结果
│   ├── val/                                # 验证结果
│   └── infer/                              # 推理结果
├── models/                                 # 模型文件
│   ├── pretrained/                         # 预训练模型
│   └── checkpoints/                        # 训练后模型
├── utilss/                                 # 自定义的一些工具
├── test                              # 自定义的一些工具

- DTHWeb
- DTHUi
```

- PM： 产品经理： 定义产品需求，撰写需求文档
- PJM: 项目经理：指定项目计划，跟踪进度，管理风险，确保按时交付
- 数据工程师：负责收集数据，清洗，标注，格式转换等等，
- 算法工程师：设计模型架构，编写训练， 推理脚本，优化性能
- 前后端工程师：前端：HTML.CSS.JS+Vue+React/后端：java(sprint boot ) python(flask,Django,FastApi)
- 测试工程师：验证，功能，性能，编写测试用例，报告bug
- DevOps工程师：CI/CD,部署环境



## 环境部署

### 传统方法，正规的方法

- 1.`Anaconda3(python3.12)`(`miniconda`)： 确保环境变量正常、
- 集成开发环境：Pycharm, vscode
- 深度学习环境搭建（pytorch GPU、CPU版本）
- GPU的安装：
  - 确保电脑有N卡。确保显卡驱动版本大于527
  - 安装cuda: 用默认设置即可，需要注意安装的cuda版本pytorch要支持，
  - 安装cudnn：将文件复制到cuda的安装目录即可，三个文件夹
  - 安装pytorch即可： 注意必须使用虚拟环境
- Yolo安装: pip install ultralytics

### 直接使用做好的虚拟环境

1. GPU环境：yolos.zip 解压到Anaconda的env目录即可
2. CPU环境：btd_cpu解压到Anaconda的env目录即可

创建好的虚拟环境，可以使用conda管理，或者直接用

### pycharm中解释器的管理和使用

**conda管理**

注意conda的路径：

![image-20250623130518679](./image/image-20250623130518679.png)

直接使用：直接选择对应的python解释器即可

![image-20250623130812574](./image/image-20250623130812574.png)

### 环境测试-GPU和CPU

```python
import torch

print(f"Pytorch版本：{torch.__version__}")
print(f"CUDA版本：{torch.version.cuda}")
print(f"cuDNN版本：{torch.backends.cudnn.version()}")

print(f"GPU设备数量：{torch.cuda.device_count()}")
print(f"GPU可用：{torch.cuda.is_available()}")

print(f"GPU设备：{torch.cuda.get_device_name(0)}")
print(f"当前显卡的总显存：{torch.cuda.get_device_properties(0).total_memory / 1024 ** 3}GB")

Pytorch版本：2.7.1+cu126
CUDA版本：12.6
cuDNN版本：90701
GPU设备数量：1
GPU可用：True
GPU设备：NVIDIA GeForce RTX 3070 Ti Laptop GPU
当前显卡的总显存：7.99951171875GB
```

```
#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :test_torch.py
# @Time      :2025/6/23 11:02:36
# <AUTHOR>
# @Project   :BTD
# @Function  :CPU测试
import torch

print(f"Pytorch版本：{torch.__version__}")
print(f"CUDA版本：{torch.version.cuda}")
print(f"cuDNN版本：{torch.backends.cudnn.version()}")

print(f"GPU设备数量：{torch.cuda.device_count()}")
print(f"GPU可用：{torch.cuda.is_available()}")
#################### yolo测试
#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :test_yolo.py
# @Time      :2025/6/23 11:09:13
# <AUTHOR>
# @Project   :BTD
# @Function  :
if __name__ == "__main__":
    from ultralytics import YOLO

    # # 从配置文件创建新的模型
    # model = YOLO("yolov8n.yaml")
    #
    # # 训练模型
    # model.train(data="coco8.yaml", epochs=3)

    # 第二步推理
    model = YOLO("best.pt")
    results = model.predict(source="0", show=True)

```

## 整体项目目录设计

我们项目的顶层目录是：BTD（Brain Tumor Detection）目录.在这个里面可以划分三个目录出来

`yoloserver`：yolo服务目录： 【重点】

`BTDWeb`: 开发Web客户端的目录

`BTDUi`开发桌面端的目录



```
yoloserver:
	- initialize_project.py # 初始化脚本：用户初始化项目，创建各种目录
	- configs # 存放模型配置，数据集配置，等Yaml文件
	- data  # 存放所有数据集相关的文件
		- raw  # 原始数据目录
			- images: 所有的原始数据图像,一般是用户提供
			- original_annotations # 原始标注文件，一般是用户提供
			- yolo_staged_labels # 统一yolo Txt格式存放区，系统生成
			
		- train  # 自动化脚本创建
			- images
			- labels
		- val   # 自动化脚本创建
			- images
			- labels
		- test  # 自动化脚本创建
			- images
			- labels
	- models  # 模型站点，自动化创建
		- checkpoints # 存放那些训练好的模型，
		- pretrained  # 存放一些预训练的模型
	- runs   # 模型训练，验证，推理的运行结果，自动化创建
	- scripts  # 各种自动化脚本：模型的训练，模型的验证，模型推理，数据转换，数据验证，顶级脚本
	- utils    # 通用工具函数和模块，路径管理，日志工具，数据转换工具，
```

```
BTD/  # 项目顶层目录，用于存放整个项目的文件和资源
├── yoloserver/  # Yolo服务目录，是项目的核心部分，负责Yolo模型的训练、验证和推理等功能
│   ├── initialize_project.py  # 初始化脚本，用于初始化项目，创建各种目录和文件结构
│   ├── configs/  # 存放模型配置、数据集配置等Yaml文件，用于定义模型参数和数据集信息
│   ├── data/  # 存放所有数据集相关的文件
│   │   ├── raw/  # 原始数据目录，存放用户提供的原始数据
│   │   │   ├── images/  # 存放所有的原始数据图像，一般是用户提供的
│   │   │   ├── original_annotations/  # 存放原始标注文件，一般是用户提供的
│   │   │   └── yolo_staged_labels/  # 统一Yolo Txt格式存放区，系统生成
│   │   ├── train/  # 自动化脚本创建的训练数据目录
│   │   │   ├── images/  # 存放训练集的图像
│   │   │   └── labels/  # 存放训练集的标注文件
│   │   ├── val/  # 自动化脚本创建的验证数据目录
│   │   │   ├── images/  # 存放验证集的图像
│   │   │   └── labels/  # 存放验证集的标注文件
│   │   └── test/  # 自动化脚本创建的测试数据目录
│   │       ├── images/  # 存放测试集的图像
│   │       └── labels/  # 存放测试集的标注文件
│   ├── models/  # 模型站点，用于存放模型文件
│   │   ├── checkpoints/  # 存放训练好的模型文件
│   │   └── pretrained/  # 存放预训练的模型文件
│   ├── runs/  # 模型训练、验证、推理的运行结果目录，自动化创建
│   ├── scripts/  # 各种自动化脚本目录
│   │   # 包括模型的训练、验证、推理、数据转换、数据验证等脚本
│   └── utils/  # 通用工具函数和模块目录
│       # 包括路径管理、日志工具、数据转换工具等
├── BTDWeb/  # 开发Web客户端的目录，用于存放Web客户端相关的代码和资源
└── BTDUi/  # 开发桌面端的目录，用于存放桌面端相关的代码和资源
```

## 任务分解：第一阶段：基础架构搭建

### 任务1：`路径管理模块设计与实现： paths.py`

```
#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :paths.py
# @Time      :2025/6/23 13:46:29
# <AUTHOR>
# @Project   :BTD
# @Function  :定义所有的路径信息
from pathlib import Path

# 项目根目录
YOLOSERVER_ROOT = Path(__file__).resolve().parents[1]

# 配置文件目录
CONFIGS_DIR = YOLOSERVER_ROOT / "configs"

# 模型目录
MODEL_DIR = YOLOSERVER_ROOT / "models"

# 训练好的模型存放的位置
CHECKPOINTS_DIR = MODEL_DIR  / "checkpoints"
# 预训练模型存放的位置
PRETRAINED_DIR = MODEL_DIR / "pretrained"


# 模型运行结果 目录
RUNS_DIR = YOLOSERVER_ROOT / "runs"

# 数据文件目录
DATA_DIR = YOLOSERVER_ROOT / "data"

# 原始数据文件目录
RAW_DATA_DIR = DATA_DIR / "raw"

# 原始图像存放目录
RAW_IMAGES_DIR = RAW_DATA_DIR / "images"

# 原始非yolo格式标注文件存放目录
ORIGINAL_ANNOTATIONS_DIR = RAW_DATA_DIR / "original_annotations"

# YOLO格式标注文件暂时存放目录
YOLO_STAGED_LABELS_DIR = RAW_DATA_DIR / "yolo_staged_labels"

# 临时文件存放目录
RAW_TEMP_DIR = RAW_DATA_DIR / "temp"

# 训练验证测试数据集存放目录
TRAIN_DIR = DATA_DIR / "train"
VAL_DIR = DATA_DIR / "val"
TEST_DIR = DATA_DIR / "test"

# 日志目录
LOGS_DIR = YOLOSERVER_ROOT / "logs"

# 训练推理脚本存放目录
SCRIPTS_DIR = YOLOSERVER_ROOT / "scripts"

# 目录自动创建
for _path in [
    YOLOSERVER_ROOT,
    CONFIGS_DIR,
    MODEL_DIR,
    RUNS_DIR,
    CHECKPOINTS_DIR,
    PRETRAINED_DIR,
    DATA_DIR,
    RAW_DATA_DIR,
    RAW_IMAGES_DIR,
    ORIGINAL_ANNOTATIONS_DIR,
    YOLO_STAGED_LABELS_DIR,
    RAW_TEMP_DIR,
    TRAIN_DIR,
    VAL_DIR,
    TEST_DIR,
    LOGS_DIR,
    SCRIPTS_DIR,
]:
    _path.mkdir(parents=True, exist_ok=True)

```

### 任务2： 开发一个日志工具：`logging_utils.py`

1. 提供一个`steup_logging`函数，作为整个项目的日志配置入口，所有需要日志功能的模块都可以调用整个函数获取配置好的日志器，
2. 灵活性和可定制性，不同的场景需要不同的日志级别或者输出方式，因为这个函数要支持参数化，`log_level`控制日志等级，`log_type`区分日志文件，`log_name`获取特定名称的日志记录器
3. 避免重复添加处理器：
4. 结构化的动态日志和文件管理，根据训练的ID，模型名称来确定最终的日志文件名称，日志文件应当根据`log_type`自动创建目录，并根据时间戳和类型进行命名，`temp_log`参数：允许在开始时使用临时命名，

```
#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :logging_utils.py
# @Time      :2025/6/23 14:28:17
# <AUTHOR>
# @Project   :BTD
# @Function  :日志相关的工具类函数
import logging
from datetime import datetime
from pathlib import Path


def setup_logger(base_path: Path, log_type: str = "general",
                model_name: str = None,
                encoding: str = "utf-8",
                log_level: int = logging.INFO,
                temp_log: bool = False,
                logger_name: str = "YOLO Default"
                ):
    """
    配置日志记录器，将日志保存到指定路径的子目录当中，并同时输出到控制台，日志文件名为类型 + 时间戳
    :param model_name: 模型训练可能需要一个模型的名字，我们可以传入日志记录器，生成带模型名的日志文件
    :param log_type: 日志的类型
    :param base_path: 日志文件的根路径
    :param encoding: 文件编码
    :param log_level: 日志等级
    :param temp_log: 是否启动临时文件名
    :param logger_name: 日志记录器的名称
    :return: logging.logger: 返回一个日志记录器实例
    """
    # 1. 构建日志文件完整的存放路径
    log_dir = base_path / log_type
    log_dir.mkdir(parents=True, exist_ok=True)

    # 2. 生成一个带时间戳的日志文件名
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    # 根据temp_log参数，生成不同的日志文件名
    prefix = "temp_" if temp_log else log_type.replace(" ", "-")
    log_filename_parts = [prefix, timestamp]
    if model_name:
        log_filename_parts.append(model_name.replace(" ", "-"))
    log_filename = "_".join(log_filename_parts) + ".log"
    log_file = log_dir / log_filename

    # 3. 获取或创建指定的名称logger实例
    logger = logging.getLogger(logger_name)
    # 设定日志记录器记录最低记录级别
    logger.setLevel(log_level)
    # 阻止日志事件传播到父级logger
    logger.propagate = False

    # 4. 需要避免重复添加日志处理器，因此先检查日志处理器列表中是否已经存在了指定的日志处理器
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)
    # 5.创建文件处理器，将日志写入到文件当中
    file_handler = logging.FileHandler(log_file, encoding=encoding)
    file_handler.setFormatter(logging.Formatter(
        "%(asctime)s - %(levelname)s - %(name)s : %(message)s"))
    # 将文件处理器添加到logger实例中
    logger.addHandler(file_handler)

    # 6.创建控制台处理器，将日志输出到控制台
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        "%(asctime)s - %(levelname)s - %(name)s : %(message)s"))
    # 将控制台处理器添加到logger实例中
    logger.addHandler(console_handler)

    # 输出一些初始化信息到日志，确认配置成功
    logger.info(f"日志记录器初始化开始".center(60, "="))
    logger.info(f"当前日志记录器的根目录: {base_path}")
    logger.info(f"当前日志记录器的名称: {logger_name}")
    logger.info(f"当前日志记录器的类型: {log_type}")
    logger.info(f"单前日志记录器的级别: {logging.getLevelName(log_level)}")
    logger.info("日志记录器初始化成功".center(60, "="))
    return logger


if __name__ == "__main__":
    from paths import LOGS_DIR
    logger_ = setup_logger(base_path=LOGS_DIR,
                        log_type="test_log", model_name=None,
                        )
    logger_.info("测试日志记录器")

```



### 任务2：性能工具`performance_utils.py`

1. 记录核心操作的执行时间，**要求在不修改源码的情况下**，**测量指定函数的执行时间**
2. 不同的函数执行时间差异交大，固定为秒，显示不直观，所以要**自动选择合适的时间单位**
3. 对于执行速度非常快点函数，单次策略受到的系统影响很大，不够准确，因此需要支持执行指定次数，返回单次平均时间，
4. 清晰的输出，我们需要让他传入logger_instance参数，将检测的结果输出到日志系统当中，要求和其他日志信息保持一致，
5. 工具名称：time_it

```
#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :performance_utils.py
# @Time      :2025/6/23 15:37:47
# <AUTHOR>
# @Project   :BTD
# @Function  :放一些性能测试的工具函数
import logging
import time
from functools import wraps


_default_logger = logging.getLogger(__name__)


def time_it(iterations: int = 1, name: str = None, logger_instance=_default_logger):
    """
    一个用于记录函数执行耗时的装饰器函数，实际使用中会传入一个日志记录器
    :param iterations: 函数执行次数，如果大于1，记录平均耗时，等于1，单次执行耗时
    :param name: 用于日志输出的函数类别名称，
    :param logger_instance: 日志记录器实例
    :return:
    """
    _logger_to_use = logger_instance if logger_instance is not None else _default_logger

    # 辅助函数：根据总秒数格式化为最合适的单位
    def _format_time_auto_unit(total_seconds: float) -> str:
        """
        根据总秒数自动选择并格式化为最合适的单位（微秒、毫秒、秒、分钟、小时）。
        """
        if total_seconds < 0.000001:  # 小于1微秒
            return f"{total_seconds * 1_000_000:.3f} 微秒"
        elif total_seconds < 0.001:  # 小于1毫秒
            return f"{total_seconds * 1_000_000:.3f} 微秒"  # 保持微秒精度
        elif total_seconds < 1.0:  # 小于1秒
            return f"{total_seconds * 1000:.3f} 毫秒"
        elif total_seconds < 60.0:  # 小于1分钟
            return f"{total_seconds:.3f} 秒"
        elif total_seconds < 3600:  # 小于1小时
            minutes = int(total_seconds // 60)
            seconds = total_seconds % 60
            return f"{minutes} 分 {seconds:.3f} 秒"
        else:  # 大于等于1小时
            hours = int(total_seconds // 3600)
            minutes = int((total_seconds % 3600) // 60)
            seconds = total_seconds % 60
            return f"{hours} 小时 {minutes} 分 {seconds:.3f} 秒"

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            func_display_name = name if name is not None else func.__name__
            total_elapsed_time = 0.0
            result = None

            for i in range(iterations):
                start_time = time.perf_counter() # 获取当前时间
                result = func(*args, **kwargs)
                end_time = time.perf_counter()  # 获取结束的时间
                total_elapsed_time += end_time - start_time
            avg_elapsed_time = total_elapsed_time / iterations
            formatted_avg_time = _format_time_auto_unit(avg_elapsed_time)
            if iterations == 1:
                _logger_to_use.info(f"新能测试：'{func_display_name}' 执行耗时: {formatted_avg_time}")
            else:
                _logger_to_use.info(f"性能测试：'{func_display_name}' 执行: {iterations} 次, 单次平均耗时: {formatted_avg_time}")
            return result
        return wrapper
    return decorator


if __name__ == "__main__":
    from logging_utils import setup_logger
    from paths import LOGS_DIR
    logger = setup_logger(base_path=LOGS_DIR, log_type="performance_test")
    @time_it(iterations=5, name="测试函数",logger_instance=logger)
    def test_function():
        time.sleep(0.5)
        print("测试函数执行完成")
    test_function()


```



### 任务3：初始化脚本【作业任务】

1. 通过初始化脚本自动创建所有必须的目录，确保无论在哪里运行，项目结构保持一致
2. 可复制性和可维护性
3. 做用户指引，提示用户那些需要自己创建和需要用户操作的目录
4. 日志功能，脚本运行需要反馈，让用户那些目录被创建了，那些是已经存在的，那些是有问题的，
5. 性能监控。自动测量脚本核心功能的执行时间。

模块导入问题



