 2025-06-24 12:13:50,135 - INFO - YOLO Initialize Project : 日志记录器已启动，日志文件保存在:C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\Logs\init_project\init_project_20250624_121350.log
 2025-06-24 12:13:50,135 - INFO - YOLO Initialize Project : 日志记录器的根目录:C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\Logs
 2025-06-24 12:13:50,135 - INFO - YOLO Initialize Project : 日志记录器的名称:YOLO Initialize Project
 2025-06-24 12:13:50,136 - INFO - YOLO Initialize Project : 日志记录器的类型:init_project
 2025-06-24 12:13:50,136 - INFO - YOLO Initialize Project : 日志记录器的级别:INFO
 2025-06-24 12:13:50,136 - INFO - YOLO Initialize Project : =========================日志记录器初始化成功=========================
 2025-06-24 12:13:50,136 - INFO - YOLO Initialize Project : ==========================开始初始化项目===========================
 2025-06-24 12:13:50,136 - INFO - YOLO Initialize Project : 当前项目的根目录为: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver
 2025-06-24 12:13:50,136 - INFO - YOLO Initialize Project : =================================检查并创建核心项目目录结构==================================
 2025-06-24 12:13:50,136 - INFO - YOLO Initialize Project :  检测到已存在的目录: configs
 2025-06-24 12:13:50,136 - INFO - YOLO Initialize Project :  检测到已存在的目录: data
 2025-06-24 12:13:50,136 - INFO - YOLO Initialize Project :  检测到已存在的目录: runs
 2025-06-24 12:13:50,137 - INFO - YOLO Initialize Project :  检测到已存在的目录: models
 2025-06-24 12:13:50,137 - INFO - YOLO Initialize Project :  检测到已存在的目录: models\checkpoints
 2025-06-24 12:13:50,137 - INFO - YOLO Initialize Project :  检测到已存在的目录: models\pretrained
 2025-06-24 12:13:50,137 - INFO - YOLO Initialize Project :  检测到已存在的目录: Logs
 2025-06-24 12:13:50,137 - INFO - YOLO Initialize Project :  检测到已存在的目录: scripts
 2025-06-24 12:13:50,137 - INFO - YOLO Initialize Project :  已经创建的目录: data\train\images
 2025-06-24 12:13:50,138 - INFO - YOLO Initialize Project :  已经创建的目录: data\val\images
 2025-06-24 12:13:50,138 - INFO - YOLO Initialize Project :  已经创建的目录: data\test\images
 2025-06-24 12:13:50,138 - INFO - YOLO Initialize Project :  已经创建的目录: data\train\labels
 2025-06-24 12:13:50,138 - INFO - YOLO Initialize Project :  已经创建的目录: data\val\labels
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project :  已经创建的目录: data\test\labels
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : =====================核心项目文件夹结构检查以及创建完成======================
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : ========================开始检查原始数据集目录=========================
 2025-06-24 12:13:50,139 - WARNING - YOLO Initialize Project : 原始原始图像文件，已经存在，但内容为空，请将原始原始图像文件放在此目录下，以便后续数据集转换
 2025-06-24 12:13:50,139 - WARNING - YOLO Initialize Project : 原始原始标注文件，已经存在，但内容为空，请将原始原始标注文件放在此目录下，以便后续数据集转换
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : ===================================项目初始化结果汇总====================================
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : 一共创建了 6 个目录
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : - C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\images
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : - C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\val\images
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : - C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\test\images
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : - C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\labels
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : - C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\val\labels
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : - C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\test\labels
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : 一共检测到 8 个 已经存在的目录
 2025-06-24 12:13:50,139 - INFO - YOLO Initialize Project : - configs
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : - data
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : - runs
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : - models
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : - models\checkpoints
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : - models\pretrained
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : - Logs
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : - scripts
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : ==================================原始数据集目录检查结果===================================
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : - data\raw\images已经存在，但为空，需要放置原始数据
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : - data\raw\original_annotations已经存在，但为空，需要放置原始数据
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : 请务必根据上述提示进行操作，特别是关于原始数据集目录的检查结果
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : ====================================初始化项目完成=====================================
 2025-06-24 12:13:50,140 - INFO - YOLO Initialize Project : 新能测试:函数 项目初始化 执行耗时:None
