2025-06-27 10:57:22,951 - INFO - YOLO Initialize Project : =========================日志记录器初始化开始=========================
2025-06-27 10:57:22,951 - INFO - YOLO Initialize Project : 当前日志记录器的根目录: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\Logs
2025-06-27 10:57:22,951 - INFO - YOLO Initialize Project : 当前日志记录器的名称: YOLO Initialize Project
2025-06-27 10:57:22,951 - INFO - YOLO Initialize Project : 当前日志记录器的类型: init_project
2025-06-27 10:57:22,951 - INFO - YOLO Initialize Project : 单前日志记录器的级别: INFO
2025-06-27 10:57:22,953 - INFO - YOLO Initialize Project : =========================日志记录器初始化成功=========================
2025-06-27 10:57:22,953 - INFO - YOLO Initialize Project : ==========================开始初始化项目===========================
2025-06-27 10:57:22,953 - INFO - YOLO Initialize Project : 当前项目的根目录为: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver
2025-06-27 10:57:22,953 - INFO - YOLO Initialize Project : =================================检查并创建核心项目目录结构==================================
2025-06-27 10:57:22,953 - INFO - YOLO Initialize Project :  检测到已存在的目录: configs
2025-06-27 10:57:22,954 - INFO - YOLO Initialize Project :  检测到已存在的目录: data
2025-06-27 10:57:22,954 - INFO - YOLO Initialize Project :  检测到已存在的目录: runs
2025-06-27 10:57:22,954 - INFO - YOLO Initialize Project :  检测到已存在的目录: models
2025-06-27 10:57:22,954 - INFO - YOLO Initialize Project :  检测到已存在的目录: models\checkpoints
2025-06-27 10:57:22,954 - INFO - YOLO Initialize Project :  检测到已存在的目录: models\pretrained
2025-06-27 10:57:22,954 - INFO - YOLO Initialize Project :  检测到已存在的目录: Logs
2025-06-27 10:57:22,954 - INFO - YOLO Initialize Project :  检测到已存在的目录: scripts
2025-06-27 10:57:22,955 - INFO - YOLO Initialize Project :  检测到已存在的目录: data\train\images
2025-06-27 10:57:22,955 - INFO - YOLO Initialize Project :  检测到已存在的目录: data\val\images
2025-06-27 10:57:22,955 - INFO - YOLO Initialize Project :  检测到已存在的目录: data\test\images
2025-06-27 10:57:22,955 - INFO - YOLO Initialize Project :  检测到已存在的目录: data\train\labels
2025-06-27 10:57:22,955 - INFO - YOLO Initialize Project :  检测到已存在的目录: data\val\labels
2025-06-27 10:57:22,955 - INFO - YOLO Initialize Project :  检测到已存在的目录: data\test\labels
2025-06-27 10:57:22,955 - INFO - YOLO Initialize Project : =====================核心项目文件夹结构检查以及创建完成======================
2025-06-27 10:57:22,955 - INFO - YOLO Initialize Project : ========================开始检查原始数据集目录=========================
2025-06-27 10:57:22,956 - INFO - YOLO Initialize Project : 原始原始图像文件，已经存在，data\raw\images包含原始文件
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : 原始原始标注文件，已经存在，data\raw\original_annotations包含原始文件
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : ===================================项目初始化结果汇总====================================
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : 本次初始化没有创建任何目录
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : 一共检测到 14 个 已经存在的目录
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - configs
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - data
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - runs
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - models
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - models\checkpoints
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - models\pretrained
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - Logs
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - scripts
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - data\train\images
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - data\val\images
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - data\test\images
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - data\train\labels
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - data\val\labels
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - data\test\labels
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : ==================================原始数据集目录检查结果===================================
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - data\raw\images已经存在
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : - data\raw\original_annotations已经存在
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : 请务必根据上述提示进行操作，特别是关于原始数据集目录的检查结果
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : ====================================初始化项目完成=====================================
2025-06-27 10:57:22,957 - INFO - YOLO Initialize Project : 新能测试:函数 项目初始化 执行耗时:None
