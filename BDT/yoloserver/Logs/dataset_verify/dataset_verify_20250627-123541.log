2025-06-27 12:35:41,794 - INFO - YOLO Validate : =========================日志记录器初始化开始=========================
2025-06-27 12:35:41,794 - INFO - YOLO Validate : 当前日志记录器的根目录: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\Logs
2025-06-27 12:35:41,794 - INFO - YOLO Validate : 当前日志记录器的名称: YOLO Validate
2025-06-27 12:35:41,794 - INFO - YOLO Validate : 当前日志记录器的类型: dataset_verify
2025-06-27 12:35:41,794 - INFO - YOLO Validate : 单前日志记录器的级别: INFO
2025-06-27 12:35:41,795 - INFO - YOLO Validate : =========================日志记录器初始化成功=========================
2025-06-27 12:35:41,801 - INFO - YOLO Validate : 开始验证数据集，模式为: FULL, 任务类型为: segmentation, 删除非法数据: True
2025-06-27 12:35:41,801 - INFO - YOLO Validate : 验证data.yaml文件配置,配置文件路径为：C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\configs\data.yaml
2025-06-27 12:35:41,801 - INFO - YOLO Validate : 当前验证任务类型为: SEGMENTATION
2025-06-27 12:35:41,802 - INFO - YOLO Validate : 数据集类别数量与配置文件一致，类别数量为：3，类别为：['glioma_tumor', 'meningioma_tumor', 'pituitary_tumor']
2025-06-27 12:35:41,802 - INFO - YOLO Validate : 验证 train 路径为: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\images
2025-06-27 12:35:41,812 - INFO - YOLO Validate : 图像目录C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\images 存在678张图像
2025-06-27 12:35:41,812 - INFO - YOLO Validate : train 验证模式为FULL，将验证所有图像
2025-06-27 12:35:42,078 - INFO - YOLO Validate : 验证 val 路径为: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\val\images
2025-06-27 12:35:42,079 - INFO - YOLO Validate : 图像目录C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\val\images 存在37张图像
2025-06-27 12:35:42,079 - INFO - YOLO Validate : val 验证模式为FULL，将验证所有图像
2025-06-27 12:35:42,091 - INFO - YOLO Validate : 验证 test 路径为: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\test\images
2025-06-27 12:35:42,092 - INFO - YOLO Validate : 图像目录C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\test\images 存在39张图像
2025-06-27 12:35:42,092 - INFO - YOLO Validate : test 验证模式为FULL，将验证所有图像
2025-06-27 12:35:42,105 - INFO - YOLO Validate : 基础数据集结构和标签内容验证通过！
2025-06-27 12:35:42,105 - INFO - YOLO Validate : 基础数据集验证完成: 通过
2025-06-27 12:35:42,106 - INFO - YOLO Validate : 开始执行分割唯一性验证
2025-06-27 12:35:42,106 - INFO - YOLO Validate : 开始验证数据集划分的唯一性（train, val, test 之间无重复图像）。
2025-06-27 12:35:42,117 - INFO - YOLO Validate : 'train' 分割包含 678 张独立图像。
2025-06-27 12:35:42,118 - INFO - YOLO Validate : 'val' 分割包含 37 张独立图像。
2025-06-27 12:35:42,120 - INFO - YOLO Validate : 'test' 分割包含 39 张独立图像。
2025-06-27 12:35:42,120 - INFO - YOLO Validate : 训练集和验证集之间没有重复图像。
2025-06-27 12:35:42,120 - INFO - YOLO Validate : 训练集和测试集之间没有重复图像。
2025-06-27 12:35:42,120 - INFO - YOLO Validate : 验证集和测试集之间没有重复图像。
2025-06-27 12:35:42,120 - INFO - YOLO Validate : 数据集分割唯一性验证通过：各子集之间没有发现重复图像。
2025-06-27 12:35:42,120 - INFO - YOLO Validate : 分割唯一性验证完成: 通过
2025-06-27 12:35:42,120 - INFO - YOLO Validate : 数据集验证完成: 通过
