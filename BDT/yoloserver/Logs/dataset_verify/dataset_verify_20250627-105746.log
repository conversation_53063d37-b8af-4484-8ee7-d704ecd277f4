2025-06-27 10:57:46,498 - INFO - YOLO Validate : =========================日志记录器初始化开始=========================
2025-06-27 10:57:46,498 - INFO - YOLO Validate : 当前日志记录器的根目录: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\Logs
2025-06-27 10:57:46,498 - INFO - YOLO Validate : 当前日志记录器的名称: YOLO Validate
2025-06-27 10:57:46,498 - INFO - YOLO Validate : 当前日志记录器的类型: dataset_verify
2025-06-27 10:57:46,498 - INFO - YOLO Validate : 单前日志记录器的级别: INFO
2025-06-27 10:57:46,498 - INFO - YOLO Validate : =========================日志记录器初始化成功=========================
2025-06-27 10:57:46,508 - INFO - YOLO Validate : 开始验证数据集，模式为: FULL, 任务类型为: segmentation, 删除非法数据: True
2025-06-27 10:57:46,508 - INFO - YOLO Validate : 验证data.yaml文件配置,配置文件路径为：C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\configs\data.yaml
2025-06-27 10:57:46,508 - INFO - YOLO Validate : 当前验证任务类型为: SEGMENTATION
2025-06-27 10:57:46,509 - INFO - YOLO Validate : 数据集类别数量与配置文件一致，类别数量为：3，类别为：['glioma_tumor', 'meningioma_tumor', 'pituitary_tumor']
2025-06-27 10:57:46,510 - INFO - YOLO Validate : 验证 train 路径为: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\images
2025-06-27 10:57:46,522 - INFO - YOLO Validate : 图像目录C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\images 存在680张图像
2025-06-27 10:57:46,522 - INFO - YOLO Validate : train 验证模式为FULL，将验证所有图像
2025-06-27 10:57:46,567 - ERROR - YOLO Validate : 标签文件格式错误: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\labels\G_363_jpg.rf.33d7401ed93615cbb89719634f9eeae5.txt，行 1: '0 0.523047 0.424609 0.149219 0.0992187' 不符合分割 YOLO 格式 (应为至少7个值，且类别ID后坐标对数量为偶数)。
2025-06-27 10:57:46,709 - ERROR - YOLO Validate : 标签文件格式错误: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\labels\P_407_jpg.rf.6598061f15a416a2ef0c45827f636f8d.txt，行 1: '2 0.512891 0.60625 0.260156 0.134375' 不符合分割 YOLO 格式 (应为至少7个值，且类别ID后坐标对数量为偶数)。
2025-06-27 10:57:46,725 - INFO - YOLO Validate : 验证 val 路径为: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\val\images
2025-06-27 10:57:46,726 - INFO - YOLO Validate : 图像目录C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\val\images 存在37张图像
2025-06-27 10:57:46,727 - INFO - YOLO Validate : val 验证模式为FULL，将验证所有图像
2025-06-27 10:57:46,738 - INFO - YOLO Validate : 验证 test 路径为: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\test\images
2025-06-27 10:57:46,739 - INFO - YOLO Validate : 图像目录C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\test\images 存在39张图像
2025-06-27 10:57:46,739 - INFO - YOLO Validate : test 验证模式为FULL，将验证所有图像
2025-06-27 10:57:46,750 - ERROR - YOLO Validate : 基础数据集结构或标签内容验证未通过，共检测到 2 个不合法的图像-标签对。请检查日志中的错误。
2025-06-27 10:57:46,750 - ERROR - YOLO Validate : 基础数据集验证未通过，请查看详细的日志
2025-06-27 10:57:46,750 - ERROR - YOLO Validate : 检测到(2)个不合法的数据样本，详细信息如下: 
2025-06-27 10:57:46,750 - ERROR - YOLO Validate : 1 不合法数据样本: {图像: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\images\G_363_jpg.rf.33d7401ed93615cbb89719634f9eeae5.jpg, 标签: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\labels\G_363_jpg.rf.33d7401ed93615cbb89719634f9eeae5.txt, 错误信息: 标签格式错误: 不符合分割 YOLO 格式 (应为至少7个值，且类别ID后坐标对数量为偶数), 行 '1: 0 0.523047 0.424609 0.149219 0.0992187'}
2025-06-27 10:57:46,750 - ERROR - YOLO Validate : 2 不合法数据样本: {图像: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\images\P_407_jpg.rf.6598061f15a416a2ef0c45827f636f8d.jpg, 标签: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\labels\P_407_jpg.rf.6598061f15a416a2ef0c45827f636f8d.txt, 错误信息: 标签格式错误: 不符合分割 YOLO 格式 (应为至少7个值，且类别ID后坐标对数量为偶数), 行 '1: 2 0.512891 0.60625 0.260156 0.134375'}
2025-06-27 10:57:46,750 - WARNING - YOLO Validate : 当前非交互式终端, 将直接删除非法数据样本
2025-06-27 10:57:46,750 - INFO - YOLO Validate : 开始删除不合法的图像和标签文件...
2025-06-27 10:57:46,750 - INFO - YOLO Validate : 成功删除图像文件: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\images\G_363_jpg.rf.33d7401ed93615cbb89719634f9eeae5.jpg
2025-06-27 10:57:46,751 - INFO - YOLO Validate : 成功删除标签文件: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\labels\G_363_jpg.rf.33d7401ed93615cbb89719634f9eeae5.txt
2025-06-27 10:57:46,751 - INFO - YOLO Validate : 成功删除图像文件: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\images\P_407_jpg.rf.6598061f15a416a2ef0c45827f636f8d.jpg
2025-06-27 10:57:46,751 - INFO - YOLO Validate : 成功删除标签文件: C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\labels\P_407_jpg.rf.6598061f15a416a2ef0c45827f636f8d.txt
2025-06-27 10:57:46,751 - INFO - YOLO Validate : 删除操作完成。共删除 2 个图像文件和 2 个标签文件。
2025-06-27 10:57:46,751 - INFO - YOLO Validate : 开始执行分割唯一性验证
2025-06-27 10:57:46,751 - INFO - YOLO Validate : 开始验证数据集划分的唯一性（train, val, test 之间无重复图像）。
2025-06-27 10:57:46,760 - INFO - YOLO Validate : 'train' 分割包含 678 张独立图像。
2025-06-27 10:57:46,761 - INFO - YOLO Validate : 'val' 分割包含 37 张独立图像。
2025-06-27 10:57:46,763 - INFO - YOLO Validate : 'test' 分割包含 39 张独立图像。
2025-06-27 10:57:46,763 - INFO - YOLO Validate : 训练集和验证集之间没有重复图像。
2025-06-27 10:57:46,763 - INFO - YOLO Validate : 训练集和测试集之间没有重复图像。
2025-06-27 10:57:46,763 - INFO - YOLO Validate : 验证集和测试集之间没有重复图像。
2025-06-27 10:57:46,763 - INFO - YOLO Validate : 数据集分割唯一性验证通过：各子集之间没有发现重复图像。
2025-06-27 10:57:46,763 - INFO - YOLO Validate : 分割唯一性验证完成: 通过
2025-06-27 10:57:46,763 - INFO - YOLO Validate : 数据集验证完成: 通过
