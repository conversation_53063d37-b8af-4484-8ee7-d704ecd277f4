import os

def check_boxes_segments(label_dir):
    """
    检查label_dir文件夹中所有txt文件的boxes和segments数量是否匹配。
    返回不匹配文件列表，格式为 (filename, boxes_count, segments_count)
    """
    mismatched_files = []

    for filename in os.listdir(label_dir):
        if not filename.endswith('.txt'):
            continue

        file_path = os.path.join(label_dir, filename)
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        boxes_count = 0
        segments_count = 0

        for line in lines:
            line = line.strip()
            if not line:
                continue

            parts = line.split()
            # YOLO分割格式：至少 class + 4 bbox + 多边形点
            # 判断是否有segments (多边形点)：行长度 > 5（class + 4 bbox + 至少一对坐标）
            if len(parts) > 5:
                segments_count += 1
                boxes_count += 1  # segment行同时包含bbox
            else:
                boxes_count += 1

        if boxes_count != segments_count:
            mismatched_files.append((filename, boxes_count, segments_count))

    return mismatched_files


def add_dummy_segments(label_dir, files_to_fix):
    """
    对boxes和segments数量不匹配的文件，给每个只有bbox的标签行补充矩形多边形占位分割点。
    """
    for filename in files_to_fix:
        file_path = os.path.join(label_dir, filename)
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        new_lines = []
        modified = False

        for line in lines:
            parts = line.strip().split()
            if len(parts) == 5:  # 只有bbox，没有segments
                cls_id = parts[0]
                x_c, y_c, w, h = map(float, parts[1:5])
                # 计算bbox四个角点（归一化坐标）
                x1, y1 = x_c - w/2, y_c - h/2
                x2, y2 = x_c + w/2, y_c - h/2
                x3, y3 = x_c + w/2, y_c + h/2
                x4, y4 = x_c - w/2, y_c + h/2
                segment_points = [x1, y1, x2, y2, x3, y3, x4, y4]
                segment_points_str = ' '.join(f'{pt:.6f}' for pt in segment_points)
                new_line = f"{cls_id} {x_c:.6f} {y_c:.6f} {w:.6f} {h:.6f} {segment_points_str}\n"
                new_lines.append(new_line)
                modified = True
            else:
                new_lines.append(line)

        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            print(f"已为文件 {filename} 补充了占位分割多边形")


if __name__ == "__main__":
    label_folder = r"C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\data\train\labels"  # 替换为你的标签路径

    mismatched = check_boxes_segments(label_folder)
    if not mismatched:
        print("所有标签文件中的boxes和segments数量均匹配。")
    else:
        print("以下文件boxes和segments数量不匹配：")
        for fname, boxes, segments in mismatched:
            print(f"文件: {fname} ，boxes={boxes}，segments={segments}")

        # 自动修复这些文件，补充占位分割
        files_to_fix = [fname for fname, _, _ in mismatched]
        add_dummy_segments(label_folder, files_to_fix)
