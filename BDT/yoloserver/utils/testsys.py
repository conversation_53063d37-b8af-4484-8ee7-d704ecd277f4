import os
import re


def check_sys_import(dir_path):
    py_files = []
    for root, dirs, files in os.walk(dir_path):
        for file in files:
            if file.endswith(".py"):
                py_files.append(os.path.join(root, file))

    missing_sys_import = []

    for file_path in py_files:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 检查是否有 sys 的引用
        uses_sys = bool(re.search(r'\bsys\b', content))
        # 检查是否导入了 sys
        imports_sys = bool(re.search(r'^\s*import\s+sys', content, re.MULTILINE)) or \
                      bool(re.search(r'^\s*from\s+sys\s+import\s+', content, re.MULTILINE))

        if uses_sys and not imports_sys:
            missing_sys_import.append(file_path)

    if missing_sys_import:
        print("以下文件用到了 sys 但未导入 sys，建议补充 import sys ：")
        for f in missing_sys_import:
            print(f" - {f}")
    else:
        print("未发现用 sys 但未导入 sys 的文件。")


if __name__ == "__main__":
    # 这里替换成你自己的 utils 文件夹路径
    utils_dir = r"C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\utils"
    check_sys_import(utils_dir)
