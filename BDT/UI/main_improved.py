#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
改进版YOLO检测系统主应用程序
作者: AzureKite
功能: 提供更稳定、更美观的UI界面
"""

import sys
import os
from pathlib import Path
import traceback

# 路径配置
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# PySide6 导入
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QListWidget, QTextBrowser, QProgressBar,
    QSizePolicy, QGraphicsDropShadowEffect, QMessageBox, QFileDialog,
    QListWidgetItem, QFrame
)
from PySide6.QtCore import Qt, QPoint, QTimer, Signal, QThread, QSize
from PySide6.QtGui import QColor, QPixmap, QFont, QIcon

class ImprovedYOLOWindow(QMainWindow):
    """改进版YOLO检测系统主窗口"""

    def __init__(self):
        super().__init__()
        self.init_variables()
        self.init_ui()
        self.setup_connections()
        self.apply_styles()

    def init_variables(self):
        """初始化变量"""
        self.dragging = False
        self.start_pos = QPoint()
        self.current_model_path = None
        self.detection_running = False

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("YOLO目标检测系统 v2.0")
        self.setGeometry(100, 100, 1400, 900)

        # 设置窗口属性
        self.setWindowFlags(Qt.Window | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # 创建中央widget
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 主布局
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(0)

        # 创建各个区域
        self.create_title_bar()
        self.create_content_area()
        self.create_status_bar()

        # 添加阴影效果
        self.add_shadow_effect()

    def create_title_bar(self):
        """创建自定义标题栏"""
        self.title_bar = QFrame()
        self.title_bar.setFixedHeight(50)
        self.title_bar.setObjectName("titleBar")

        title_layout = QHBoxLayout(self.title_bar)
        title_layout.setContentsMargins(15, 0, 15, 0)

        # 应用图标和标题
        self.app_icon = QLabel()
        self.app_icon.setFixedSize(32, 32)
        self.app_icon.setStyleSheet("background-color: #0078d4; border-radius: 16px;")

        self.app_title = QLabel("YOLO目标检测系统")
        self.app_title.setObjectName("appTitle")

        # 窗口控制按钮
        self.create_window_controls()

        title_layout.addWidget(self.app_icon)
        title_layout.addSpacing(10)
        title_layout.addWidget(self.app_title)
        title_layout.addStretch()
        title_layout.addWidget(self.btn_minimize)
        title_layout.addWidget(self.btn_maximize)
        title_layout.addWidget(self.btn_close)

        self.main_layout.addWidget(self.title_bar)

    def create_window_controls(self):
        """创建窗口控制按钮"""
        button_size = QSize(35, 35)

        self.btn_minimize = QPushButton("−")
        self.btn_maximize = QPushButton("□")
        self.btn_close = QPushButton("×")

        for btn in [self.btn_minimize, self.btn_maximize, self.btn_close]:
            btn.setFixedSize(button_size)
            btn.setObjectName("windowButton")

        self.btn_close.setObjectName("closeButton")

    def create_content_area(self):
        """创建主要内容区域"""
        self.content_frame = QFrame()
        self.content_frame.setObjectName("contentFrame")

        content_layout = QHBoxLayout(self.content_frame)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(1)

        # 左侧导航面板
        self.create_navigation_panel(content_layout)

        # 中间主显示区域
        self.create_main_display_area(content_layout)

        # 右侧控制面板
        self.create_control_panel(content_layout)

        self.main_layout.addWidget(self.content_frame, 1)

    def create_navigation_panel(self, parent_layout):
        """创建左侧导航面板"""
        self.nav_panel = QFrame()
        self.nav_panel.setFixedWidth(250)
        self.nav_panel.setObjectName("navPanel")

        nav_layout = QVBoxLayout(self.nav_panel)
        nav_layout.setContentsMargins(10, 10, 10, 10)

        # 导航标题
        nav_title = QLabel("功能导航")
        nav_title.setObjectName("navTitle")
        nav_layout.addWidget(nav_title)

        # 功能菜单
        self.menu_list = QListWidget()
        self.menu_list.setObjectName("menuList")

        menu_items = [
            ("🎯", "模型选择"),
            ("🖼️", "图像检测"),
            ("🎬", "视频检测"),
            ("📹", "摄像头检测"),
            ("📁", "批量检测"),
            ("⚙️", "检测设置"),
            ("📊", "模型管理"),
            ("ℹ️", "关于系统")
        ]

        for icon, text in menu_items:
            item = QListWidgetItem(f"{icon} {text}")
            item.setData(Qt.UserRole, text)
            self.menu_list.addItem(item)

        nav_layout.addWidget(self.menu_list)

        # Logo区域
        self.logo_area = QLabel("YOLO\nDETECTION")
        self.logo_area.setFixedHeight(120)
        self.logo_area.setObjectName("logoArea")
        self.logo_area.setAlignment(Qt.AlignCenter)
        nav_layout.addWidget(self.logo_area)

        parent_layout.addWidget(self.nav_panel)

    def create_main_display_area(self, parent_layout):
        """创建中间主显示区域"""
        main_area = QFrame()
        main_area.setObjectName("mainArea")

        main_layout = QVBoxLayout(main_area)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 主显示标签
        self.main_display = QLabel()
        self.main_display.setObjectName("mainDisplay")
        self.main_display.setAlignment(Qt.AlignCenter)
        self.main_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.set_welcome_message()

        main_layout.addWidget(self.main_display, 1)

        # 控制按钮区域
        self.create_control_buttons(main_layout)

        parent_layout.addWidget(main_area, 1)

    def create_control_buttons(self, parent_layout):
        """创建控制按钮区域"""
        control_frame = QFrame()
        control_frame.setFixedHeight(70)
        control_frame.setObjectName("controlFrame")

        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(10, 10, 10, 10)

        # 控制按钮
        self.btn_start = QPushButton("🚀 开始检测")
        self.btn_stop = QPushButton("⏹️ 停止检测")
        self.btn_clear = QPushButton("🗑️ 清除结果")

        for btn in [self.btn_start, self.btn_stop, self.btn_clear]:
            btn.setFixedHeight(45)
            btn.setObjectName("controlButton")

        self.btn_stop.setEnabled(False)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setVisible(False)

        control_layout.addWidget(self.btn_start)
        control_layout.addWidget(self.btn_stop)
        control_layout.addWidget(self.btn_clear)
        control_layout.addStretch()
        control_layout.addWidget(self.progress_bar, 1)

        parent_layout.addWidget(control_frame)

    def create_control_panel(self, parent_layout):
        """创建右侧控制面板"""
        self.control_panel = QFrame()
        self.control_panel.setFixedWidth(320)
        self.control_panel.setObjectName("controlPanel")

        panel_layout = QVBoxLayout(self.control_panel)
        panel_layout.setContentsMargins(10, 10, 10, 10)

        # 文件上传区域
        self.create_upload_area(panel_layout)

        # 结果显示区域
        self.create_result_area(panel_layout)

        parent_layout.addWidget(self.control_panel)