# -*- coding: utf-8 -*-

# Resource object code (Python 3)
# Created by: object code
# Created by: The Resource Compiler for Qt version 6.9.0
# WARNING! All changes made in this file will be lost!

from PySide6 import QtCore

qt_resource_data = b""

qt_resource_name = b""

qt_resource_struct = b""

def qInitResources():
    QtCore.qRegisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

def qCleanupResources():
    QtCore.qUnregisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

qInitResources()
