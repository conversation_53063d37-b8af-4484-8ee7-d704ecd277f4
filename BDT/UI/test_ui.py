#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QListWidget, QTextBrowser, QProgressBar,
    QSizePolicy, QGraphicsDropShadowEffect
)
from PySide6.QtCore import Qt, QPoint
from PySide6.QtGui import QColor, QPixmap, QFont

class SimpleTestWindow(QMainWindow):
    """简化的测试窗口，用于验证基本UI功能"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_window_properties()
        
    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle("YOLO检测系统 - 测试版本")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 顶部标题栏
        self.create_title_bar(main_layout)
        
        # 中间内容区域
        self.create_content_area(main_layout)
        
        # 底部状态区域
        self.create_bottom_area(main_layout)
        
    def create_title_bar(self, parent_layout):
        """创建标题栏"""
        title_widget = QWidget()
        title_widget.setFixedHeight(50)
        title_widget.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
        """)
        
        title_layout = QHBoxLayout(title_widget)
        
        # 标题文本
        title_label = QLabel("YOLO目标检测系统")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                background: transparent;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # 控制按钮
        btn_min = QPushButton("−")
        btn_max = QPushButton("□")
        btn_close = QPushButton("×")
        
        for btn in [btn_min, btn_max, btn_close]:
            btn.setFixedSize(30, 30)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    color: white;
                    border: none;
                    font-size: 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #555;
                    border-radius: 4px;
                }
            """)
        
        btn_close.setStyleSheet(btn_close.styleSheet() + """
            QPushButton:hover {
                background-color: #e81123;
            }
        """)
        
        # 连接信号
        btn_min.clicked.connect(self.showMinimized)
        btn_max.clicked.connect(self.toggle_maximize)
        btn_close.clicked.connect(self.close)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(btn_min)
        title_layout.addWidget(btn_max)
        title_layout.addWidget(btn_close)
        
        parent_layout.addWidget(title_widget)
        
    def create_content_area(self, parent_layout):
        """创建主要内容区域"""
        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        
        # 左侧导航菜单
        self.create_navigation_menu(content_layout)
        
        # 中间显示区域
        self.create_main_display(content_layout)
        
        # 右侧控制面板
        self.create_control_panel(content_layout)
        
        parent_layout.addWidget(content_widget)
        
    def create_navigation_menu(self, parent_layout):
        """创建左侧导航菜单"""
        nav_widget = QWidget()
        nav_widget.setFixedWidth(200)
        nav_widget.setStyleSheet("""
            QWidget {
                background-color: #3c3c3c;
                border: none;
            }
        """)
        
        nav_layout = QVBoxLayout(nav_widget)
        
        # 菜单标题
        menu_title = QLabel("功能菜单")
        menu_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                background-color: #4a4a4a;
                border-radius: 4px;
                margin-bottom: 10px;
            }
        """)
        menu_title.setAlignment(Qt.AlignCenter)
        nav_layout.addWidget(menu_title)
        
        # 菜单项
        menu_items = [
            "模型选择",
            "图像检测", 
            "视频检测",
            "摄像头检测",
            "文件夹检测",
            "检测设置",
            "模型管理",
            "关于我们"
        ]
        
        self.menu_list = QListWidget()
        self.menu_list.setStyleSheet("""
            QListWidget {
                background-color: transparent;
                border: none;
                color: white;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 8px;
                border-radius: 4px;
                margin: 2px;
            }
            QListWidget::item:hover {
                background-color: #555;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
            }
        """)
        
        for item in menu_items:
            self.menu_list.addItem(item)
            
        self.menu_list.itemClicked.connect(self.on_menu_clicked)
        nav_layout.addWidget(self.menu_list)
        
        # Logo区域
        logo_label = QLabel("LOGO")
        logo_label.setFixedHeight(150)
        logo_label.setStyleSheet("""
            QLabel {
                background-color: #2a2a2a;
                color: #888;
                border-radius: 8px;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        logo_label.setAlignment(Qt.AlignCenter)
        nav_layout.addWidget(logo_label)
        
        parent_layout.addWidget(nav_widget)
        
    def create_main_display(self, parent_layout):
        """创建中间主显示区域"""
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        
        # 主显示标签
        self.main_display = QLabel("欢迎使用YOLO目标检测系统\n\n请选择左侧菜单开始使用")
        self.main_display.setStyleSheet("""
            QLabel {
                background-color: #404040;
                color: white;
                font-size: 16px;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        self.main_display.setAlignment(Qt.AlignCenter)
        self.main_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        main_layout.addWidget(self.main_display)
        
        # 控制按钮区域
        control_widget = QWidget()
        control_widget.setFixedHeight(60)
        control_layout = QHBoxLayout(control_widget)
        
        self.start_btn = QPushButton("开始检测")
        self.stop_btn = QPushButton("停止检测")
        self.progress_bar = QProgressBar()
        
        for btn in [self.start_btn, self.stop_btn]:
            btn.setFixedHeight(40)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #0078d4;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 16px;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
                QPushButton:pressed {
                    background-color: #005a9e;
                }
            """)
            
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #555;
                border-radius: 4px;
                text-align: center;
                color: white;
                background-color: #2a2a2a;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 2px;
            }
        """)
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.progress_bar, 1)
        
        main_layout.addWidget(control_widget)
        parent_layout.addWidget(main_widget, 2)
        
    def create_control_panel(self, parent_layout):
        """创建右侧控制面板"""
        panel_widget = QWidget()
        panel_widget.setFixedWidth(300)
        panel_layout = QVBoxLayout(panel_widget)
        
        # 上传区域
        upload_label = QLabel("拖拽文件到此处\n或点击选择文件")
        upload_label.setFixedHeight(200)
        upload_label.setStyleSheet("""
            QLabel {
                background-color: #3a3a3a;
                color: #aaa;
                border: 2px dashed #666;
                border-radius: 8px;
                font-size: 14px;
            }
        """)
        upload_label.setAlignment(Qt.AlignCenter)
        panel_layout.addWidget(upload_label)
        
        # 结果显示区域
        result_title = QLabel("检测结果")
        result_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
            }
        """)
        panel_layout.addWidget(result_title)
        
        self.result_text = QTextBrowser()
        self.result_text.setStyleSheet("""
            QTextBrowser {
                background-color: #2a2a2a;
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }
        """)
        self.result_text.setHtml("""
        <p><b>作者:</b> AzureKite</p>
        <p><b>邮箱:</b> <EMAIL></p>
        <p><b>QQ:</b> 910014191</p>
        <br>
        <p>系统就绪，等待操作...</p>
        """)
        panel_layout.addWidget(self.result_text)
        
        parent_layout.addWidget(panel_widget)
        
    def create_bottom_area(self, parent_layout):
        """创建底部区域"""
        bottom_widget = QWidget()
        bottom_widget.setFixedHeight(100)
        bottom_layout = QVBoxLayout(bottom_widget)
        
        log_title = QLabel("系统日志")
        log_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                font-weight: bold;
                padding: 2px;
            }
        """)
        bottom_layout.addWidget(log_title)
        
        self.log_text = QTextBrowser()
        self.log_text.setStyleSheet("""
            QTextBrowser {
                background-color: #1e1e1e;
                color: #ddd;
                border: 1px solid #555;
                border-radius: 4px;
                font-family: 'Consolas', monospace;
                font-size: 11px;
            }
        """)
        self.log_text.append("[INFO] 系统启动完成")
        self.log_text.append("[INFO] UI界面加载成功")
        bottom_layout.addWidget(self.log_text)
        
        parent_layout.addWidget(bottom_widget)
        
    def setup_window_properties(self):
        """设置窗口属性"""
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                border-radius: 8px;
            }
        """)
        
        # 设置无边框窗口
        self.setWindowFlags(Qt.Window | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 120))
        shadow.setOffset(0, 0)
        self.setGraphicsEffect(shadow)
        
        # 窗口拖动相关
        self.dragging = False
        self.start_pos = QPoint()
        
    def on_menu_clicked(self, item):
        """处理菜单点击事件"""
        menu_text = item.text()
        self.main_display.setText(f"您选择了: {menu_text}\n\n功能开发中...")
        self.log_text.append(f"[INFO] 用户选择了菜单: {menu_text}")
        
    def toggle_maximize(self):
        """切换最大化状态"""
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()
            
    def mousePressEvent(self, event):
        """鼠标按下事件 - 用于窗口拖动"""
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.start_pos = event.globalPosition().toPoint() - self.pos()
            
    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 用于窗口拖动"""
        if self.dragging:
            self.move(event.globalPosition().toPoint() - self.start_pos)
            
    def mouseReleaseEvent(self, event):
        """鼠标释放事件 - 结束窗口拖动"""
        if event.button() == Qt.LeftButton:
            self.dragging = False

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    window = SimpleTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
