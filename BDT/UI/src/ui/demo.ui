<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1676</width>
    <height>1006</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>5</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>5</number>
    </property>
    <item>
     <layout class="QVBoxLayout" name="verticalLayout" stretch="0,10,2">
      <item>
       <widget class="QWidget" name="TOP" native="true">
        <layout class="QHBoxLayout" name="top">
         <item>
          <layout class="QHBoxLayout" name="topBarLayout">
           <item>
            <widget class="QWidget" name="topBarWidget" native="true">
             <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="9,1">
              <item>
               <layout class="QHBoxLayout" name="title">
                <item>
                 <widget class="QLabel" name="protitle">
                  <property name="text">
                   <string>This is system Name</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="button">
                <item>
                 <widget class="QPushButton" name="btn_min">
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="../../resources/resources.qrc">
                    <normaloff>:/icons/assets/icons/btn_min.png</normaloff>:/icons/assets/icons/btn_min.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_max">
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="../../resources/resources.qrc">
                    <normaloff>:/icons/assets/icons/btn_max.png</normaloff>:/icons/assets/icons/btn_max.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_close">
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset>
                    <normaloff>:/icons/assets/icons/btn_close.png</normaloff>:/icons/assets/icons/btn_close.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="MID" native="true">
        <layout class="QHBoxLayout" name="mid">
         <property name="spacing">
          <number>2</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>2</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>2</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="midlayout" stretch="3,9,3">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>3</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>3</number>
           </property>
           <item>
            <widget class="QWidget" name="midl" native="true">
             <property name="minimumSize">
              <size>
               <width>260</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>260</width>
               <height>16777215</height>
              </size>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_2">
              <property name="spacing">
               <number>3</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>3</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <layout class="QVBoxLayout" name="midllayout" stretch="5,2">
                <property name="topMargin">
                 <number>2</number>
                </property>
                <property name="bottomMargin">
                 <number>2</number>
                </property>
                <item>
                 <widget class="QWidget" name="listwidget" native="true">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>阿里巴巴普惠体 2.0</family>
                    <pointsize>13</pointsize>
                   </font>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_7">
                   <item>
                    <layout class="QVBoxLayout" name="qlistlay">
                     <item>
                      <widget class="QListWidget" name="qlist">
                       <property name="font">
                        <font>
                         <family>LXGW WenKai GB Screen</family>
                         <pointsize>14</pointsize>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="layoutDirection">
                        <enum>Qt::LayoutDirection::LeftToRight</enum>
                       </property>
                       <property name="lineWidth">
                        <number>2</number>
                       </property>
                       <property name="selectionMode">
                        <enum>QAbstractItemView::SelectionMode::SingleSelection</enum>
                       </property>
                       <property name="iconSize">
                        <size>
                         <width>28</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="textElideMode">
                        <enum>Qt::TextElideMode::ElideMiddle</enum>
                       </property>
                       <property name="movement">
                        <enum>QListView::Movement::Free</enum>
                       </property>
                       <property name="isWrapping" stdset="0">
                        <bool>false</bool>
                       </property>
                       <property name="spacing">
                        <number>3</number>
                       </property>
                       <property name="viewMode">
                        <enum>QListView::ViewMode::ListMode</enum>
                       </property>
                       <property name="itemAlignment">
                        <set>Qt::AlignmentFlag::AlignCenter</set>
                       </property>
                       <item>
                        <property name="text">
                         <string>模型选择</string>
                        </property>
                        <property name="font">
                         <font>
                          <pointsize>14</pointsize>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="icon">
                         <iconset resource="../../resources/resources.qrc">
                          <normaloff>:/icons/assets/icons/model_select.png</normaloff>:/icons/assets/icons/model_select.png</iconset>
                        </property>
                       </item>
                       <item>
                        <property name="text">
                         <string>图像检测</string>
                        </property>
                        <property name="font">
                         <font>
                          <pointsize>14</pointsize>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="icon">
                         <iconset resource="../../resources/resources.qrc">
                          <normaloff>:/icons/assets/icons/image.png</normaloff>:/icons/assets/icons/image.png</iconset>
                        </property>
                       </item>
                       <item>
                        <property name="text">
                         <string>视频检测</string>
                        </property>
                        <property name="font">
                         <font>
                          <pointsize>14</pointsize>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="icon">
                         <iconset resource="../../resources/resources.qrc">
                          <normaloff>:/icons/assets/icons/video.png</normaloff>:/icons/assets/icons/video.png</iconset>
                        </property>
                       </item>
                       <item>
                        <property name="text">
                         <string>摄像头检测</string>
                        </property>
                        <property name="font">
                         <font>
                          <pointsize>14</pointsize>
                          <italic>false</italic>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="icon">
                         <iconset resource="../../resources/resources.qrc">
                          <normaloff>:/icons/assets/icons/camera.png</normaloff>:/icons/assets/icons/camera.png</iconset>
                        </property>
                       </item>
                       <item>
                        <property name="text">
                         <string>文件夹检测</string>
                        </property>
                        <property name="font">
                         <font>
                          <pointsize>14</pointsize>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="icon">
                         <iconset resource="../../resources/resources.qrc">
                          <normaloff>:/icons/assets/icons/dir.png</normaloff>:/icons/assets/icons/dir.png</iconset>
                        </property>
                       </item>
                       <item>
                        <property name="text">
                         <string>检测设置</string>
                        </property>
                        <property name="font">
                         <font>
                          <pointsize>14</pointsize>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="icon">
                         <iconset resource="../../resources/resources.qrc">
                          <normaloff>:/icons/assets/icons/setting.png</normaloff>:/icons/assets/icons/setting.png</iconset>
                        </property>
                       </item>
                       <item>
                        <property name="text">
                         <string>模型管理</string>
                        </property>
                        <property name="icon">
                         <iconset resource="../../resources/resources.qrc">
                          <normaloff>:/icons/assets/icons/modelmanager.png</normaloff>:/icons/assets/icons/modelmanager.png</iconset>
                        </property>
                       </item>
                       <item>
                        <property name="text">
                         <string>关于我们</string>
                        </property>
                        <property name="font">
                         <font>
                          <pointsize>14</pointsize>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="icon">
                         <iconset resource="../../resources/resources.qrc">
                          <normaloff>:/icons/assets/icons/about.png</normaloff>:/icons/assets/icons/about.png</iconset>
                        </property>
                       </item>
                      </widget>
                     </item>
                    </layout>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QWidget" name="logowidget" native="true">
                  <property name="minimumSize">
                   <size>
                    <width>260</width>
                    <height>260</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>260</width>
                    <height>260</height>
                   </size>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_6">
                   <item>
                    <layout class="QVBoxLayout" name="logolayout">
                     <item>
                      <widget class="QLabel" name="logo">
                       <property name="minimumSize">
                        <size>
                         <width>260</width>
                         <height>260</height>
                        </size>
                       </property>
                       <property name="maximumSize">
                        <size>
                         <width>260</width>
                         <height>260</height>
                        </size>
                       </property>
                       <property name="baseSize">
                        <size>
                         <width>350</width>
                         <height>350</height>
                        </size>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                       <property name="pixmap">
                        <pixmap resource="../../resources/resources.qrc">:/images/assets/images/png.png</pixmap>
                       </property>
                       <property name="scaledContents">
                        <bool>true</bool>
                       </property>
                       <property name="alignment">
                        <set>Qt::AlignmentFlag::AlignCenter</set>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="midm" native="true">
             <property name="minimumSize">
              <size>
               <width>1000</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_4" stretch="10,0">
              <property name="leftMargin">
               <number>3</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>3</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <layout class="QHBoxLayout" name="midmt">
                <item>
                 <widget class="QWidget" name="midmtqw" native="true">
                  <property name="minimumSize">
                   <size>
                    <width>1000</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_5">
                   <property name="leftMargin">
                    <number>2</number>
                   </property>
                   <property name="rightMargin">
                    <number>2</number>
                   </property>
                   <item>
                    <widget class="QLabel" name="detectresult">
                     <property name="minimumSize">
                      <size>
                       <width>1000</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>Main Window</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignmentFlag::AlignCenter</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="midmb">
                <item>
                 <widget class="QWidget" name="midmbqw" native="true">
                  <layout class="QHBoxLayout" name="horizontalLayout_3">
                   <item>
                    <widget class="QPushButton" name="startb">
                     <property name="text">
                      <string/>
                     </property>
                     <property name="icon">
                      <iconset resource="../../resources/resources.qrc">
                       <normaloff>:/icons/assets/icons/start.png</normaloff>:/icons/assets/icons/start.png</iconset>
                     </property>
                     <property name="iconSize">
                      <size>
                       <width>30</width>
                       <height>30</height>
                      </size>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QPushButton" name="stopb">
                     <property name="text">
                      <string/>
                     </property>
                     <property name="icon">
                      <iconset resource="../../resources/resources.qrc">
                       <normaloff>:/icons/assets/icons/stop.png</normaloff>:/icons/assets/icons/stop.png</iconset>
                     </property>
                     <property name="iconSize">
                      <size>
                       <width>30</width>
                       <height>30</height>
                      </size>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QProgressBar" name="progressBar">
                     <property name="value">
                      <number>24</number>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QPushButton" name="endp">
                     <property name="text">
                      <string/>
                     </property>
                     <property name="icon">
                      <iconset resource="../../resources/resources.qrc">
                       <normaloff>:/icons/assets/icons/end.png</normaloff>:/icons/assets/icons/end.png</iconset>
                     </property>
                     <property name="iconSize">
                      <size>
                       <width>30</width>
                       <height>30</height>
                      </size>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="midr" native="true">
             <property name="minimumSize">
              <size>
               <width>400</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>400</width>
               <height>16777215</height>
              </size>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_3">
              <property name="leftMargin">
               <number>3</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>3</number>
              </property>
              <item>
               <layout class="QVBoxLayout" name="midrlay" stretch="1,1">
                <property name="topMargin">
                 <number>2</number>
                </property>
                <property name="bottomMargin">
                 <number>2</number>
                </property>
                <item>
                 <widget class="QWidget" name="uploadim" native="true">
                  <property name="minimumSize">
                   <size>
                    <width>400</width>
                    <height>400</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>400</width>
                    <height>400</height>
                   </size>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LayoutDirection::LeftToRight</enum>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_13">
                   <item>
                    <widget class="QLabel" name="upload">
                     <property name="minimumSize">
                      <size>
                       <width>400</width>
                       <height>400</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>400</width>
                       <height>400</height>
                      </size>
                     </property>
                     <property name="layoutDirection">
                      <enum>Qt::LayoutDirection::LeftToRight</enum>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                     <property name="pixmap">
                      <pixmap resource="../../resources/resources.qrc">:/images/assets/images/png.png</pixmap>
                     </property>
                     <property name="scaledContents">
                      <bool>true</bool>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignmentFlag::AlignCenter</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QWidget" name="result" native="true">
                  <layout class="QVBoxLayout" name="verticalLayout_14">
                   <property name="leftMargin">
                    <number>2</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <widget class="QTextBrowser" name="textBrowser">
                     <property name="html">
                      <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'苹方 粗体'; font-size:12pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-weight:700;&quot;&gt;Author&lt;/span&gt;&lt;span style=&quot; font-weight:500;&quot;&gt;: AzureKite&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-weight:500;&quot;&gt;&lt;br /&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-weight:700;&quot;&gt;Email&lt;/span&gt;&lt;span style=&quot; font-weight:500;&quot;&gt;: <EMAIL>&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-weight:500;&quot;&gt;&lt;br /&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-weight:700;&quot;&gt;QQ&lt;/span&gt;&lt;span style=&quot; font-weight:500;&quot;&gt;: 910014191&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-weight:500;&quot;&gt;&lt;br /&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <layout class="QVBoxLayout" name="BUTTOM">
        <item>
         <widget class="QPlainTextEdit" name="logOutputTextEdit"/>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="../../resources/resources.qrc"/>
 </resources>
 <connections/>
</ui>
