from PySide6.QtWidgets import QApplication, QLabel
from PySide6.QtGui import QPixmap
import sys

app = QApplication(sys.argv)

image_path = r"C:\Users\<USER>\Desktop\生产实习\BDT\yoloserver\runs\infer\exp3\P_28_jpg.rf.9ee0fb5d00b6ad095dcd8cf31a10bb71.jpg"
pixmap = QPixmap(image_path)
if pixmap.isNull():
    print("图片加载失败，请检查路径是否正确或图片是否存在")
else:
    label = QLabel()
    label.setPixmap(pixmap)
    label.setWindowTitle("图片显示")
    label.resize(pixmap.width(), pixmap.height())
    label.show()
    app.exec()
